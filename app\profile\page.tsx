'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

export default function ProfilePage() {
  const { data: session, update } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  // 分别的错误和消息状态
  const [usernameError, setUsernameError] = useState('')
  const [usernameMessage, setUsernameMessage] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [passwordMessage, setPasswordMessage] = useState('')
  const [deleteError, setDeleteError] = useState('')
  const [deleteMessage, setDeleteMessage] = useState('')
  
  // 用户名修改相关状态
  const [isEditingUsername, setIsEditingUsername] = useState(false)
  const [newUsername, setNewUsername] = useState('')
  
  // 密码修改相关状态
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  // 账户注销相关状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteConfirmText, setDeleteConfirmText] = useState('')
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    setNewUsername(session.user.username || '')
  }, [session, router])

  // 修改用户名
  const handleUsernameUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newUsername.trim()) {
      setUsernameError('用户名不能为空')
      return
    }

    setLoading(true)
    setUsernameError('')
    setUsernameMessage('')

    try {
      const response = await fetch('/api/user/update-username', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username: newUsername.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        // 先更新session以显示新用户名
        await update()
        // 然后显示成功消息和关闭编辑状态
        setUsernameMessage('用户名和昵称修改成功')
        setIsEditingUsername(false)
        // 延迟清除成功消息
        setTimeout(() => {
          setUsernameMessage('')
        }, 3000)
      } else {
        setUsernameError(data.error || '修改失败')
      }
    } catch {
      setUsernameError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 修改密码
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()

    if (newPassword.length < 6) {
      setPasswordError('新密码长度至少为6位')
      return
    }

    if (newPassword !== confirmPassword) {
      setPasswordError('两次输入的新密码不一致')
      return
    }

    setLoading(true)
    setPasswordError('')
    setPasswordMessage('')

    try {
      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      })

      const data = await response.json()

      if (response.ok) {
        setPasswordMessage('密码修改成功')
        setIsChangingPassword(false)
        setCurrentPassword('')
        setNewPassword('')
        setConfirmPassword('')
        setShowCurrentPassword(false)
        setShowNewPassword(false)
        setShowConfirmPassword(false)
        // 延迟清除成功消息
        setTimeout(() => {
          setPasswordMessage('')
        }, 3000)
      } else {
        setPasswordError(data.error || '密码修改失败')
      }
    } catch {
      setPasswordError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 注销账户
  const handleDeleteAccount = async () => {
    if (deleteConfirmText !== '确认注销') {
      setDeleteError('请输入"确认注销"来确认操作')
      return
    }

    setLoading(true)
    setDeleteError('')

    try {
      const response = await fetch('/api/user/delete-account', {
        method: 'DELETE'
      })

      if (response.ok) {
        // 显示自定义成功模态框
        setShowSuccessModal(true)
        // 3秒后跳转到首页
        setTimeout(() => {
          // 使用window.location.replace确保不能通过后退按钮返回
          window.location.replace('/')
        }, 3000)
      } else {
        // 只在失败时解析JSON
        let errorMessage = '注销失败，请稍后重试'
        try {
          const data = await response.json()
          errorMessage = data.error || errorMessage
        } catch {
          // 如果JSON解析失败，使用默认错误消息
        }
        setDeleteError(errorMessage)
      }
    } catch (error) {
      console.error('注销账户错误:', error)
      setDeleteError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
          <p className="text-gray-600 mt-2">查看和管理您的账户信息</p>
        </div>



        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：基本信息 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">基本信息</h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  {session.user.userType === 'beta' ? (
                    <div className="w-12 h-12 flex items-center justify-center">
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src="/icon.svg"
                        alt="VIP"
                        className="w-10 h-10"
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full flex items-center justify-center">
                      <span className="text-white text-lg font-bold">
                        {session.user.username?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <div>
                    <p className="font-medium text-gray-900">{session.user.username}</p>
                    <p className="text-sm text-gray-500">{session.user.email}</p>
                  </div>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">经验值</span>
                    <span className="text-sm font-medium text-blue-600">{session.user.score} EXP</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">账户类型</span>
                    <span className={`text-sm font-medium ${
                      session.user.userType === 'beta' ? 'text-yellow-600' :
                      session.user.userType === 'friend' ? 'text-purple-600' : 'text-gray-600'
                    }`}>
                      {session.user.userType === 'beta' ? '内测用户' :
                       session.user.userType === 'friend' ? '好友用户' : '普通用户'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧：设置选项 */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              {/* 修改用户名 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">修改用户名/昵称</h3>
                  {!isEditingUsername && (
                    <button
                      onClick={() => setIsEditingUsername(true)}
                      className="text-green-600 hover:text-green-700 text-sm font-medium"
                    >
                      修改
                    </button>
                  )}
                </div>

                {/* 用户名成功提示 - 移到外面，这样关闭编辑状态后仍能显示 */}
                {usernameMessage && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                    <p className="text-green-700 text-sm">{usernameMessage}</p>
                  </div>
                )}
                
                {isEditingUsername ? (
                  <form onSubmit={handleUsernameUpdate} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        新用户名和昵称
                      </label>
                      <input
                        type="text"
                        value={newUsername}
                        onChange={(e) => setNewUsername(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        placeholder="请输入新用户名/昵称"
                        required
                      />
                    </div>

                    {/* 用户名错误提示 */}
                    {usernameError && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-700 text-sm">{usernameError}</p>
                      </div>
                    )}

                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        disabled={loading}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 text-sm font-medium"
                      >
                        {loading ? '保存中...' : '保存'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditingUsername(false)
                          setNewUsername(session.user.username || '')
                          setUsernameError('')
                          setUsernameMessage('')
                        }}
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 text-sm font-medium"
                      >
                        取消
                      </button>
                    </div>
                  </form>
                ) : (
                  <p className="text-gray-600">当前用户名和昵称：{session.user.username}</p>
                )}
              </div>

              {/* 修改密码 */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">修改密码</h3>
                  {!isChangingPassword && (
                    <button
                      onClick={() => setIsChangingPassword(true)}
                      className="text-green-600 hover:text-green-700 text-sm font-medium"
                    >
                      修改
                    </button>
                  )}
                </div>

                {/* 密码成功提示 - 移到外面，这样关闭编辑状态后仍能显示 */}
                {passwordMessage && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                    <p className="text-green-700 text-sm">{passwordMessage}</p>
                  </div>
                )}
                
                {isChangingPassword ? (
                  <form onSubmit={handlePasswordChange} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        当前密码
                      </label>
                      <div className="relative">
                        <input
                          type={showCurrentPassword ? "text" : "password"}
                          value={currentPassword}
                          onChange={(e) => setCurrentPassword(e.target.value)}
                          className="w-full px-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        >
                          {showCurrentPassword ? (
                            <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.243 4.243L9.88 9.88" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        新密码
                      </label>
                      <div className="relative">
                        <input
                          type={showNewPassword ? "text" : "password"}
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          className="w-full px-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="至少6位字符"
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? (
                            <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.243 4.243L9.88 9.88" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        确认新密码
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? "text" : "password"}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="w-full px-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="再次输入新密码"
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 11-4.243-4.243m4.243 4.243L9.88 9.88" />
                            </svg>
                          ) : (
                            <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    {/* 密码错误提示 */}
                    {passwordError && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-700 text-sm">{passwordError}</p>
                      </div>
                    )}

                    <div className="flex space-x-3">
                      <button
                        type="submit"
                        disabled={loading}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 text-sm font-medium"
                      >
                        {loading ? '修改中...' : '修改密码'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setIsChangingPassword(false)
                          setCurrentPassword('')
                          setNewPassword('')
                          setConfirmPassword('')
                          setPasswordError('')
                          setPasswordMessage('')
                        }}
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 text-sm font-medium"
                      >
                        取消
                      </button>
                    </div>
                  </form>
                ) : (
                  <p className="text-gray-600">点击&ldquo;修改&rdquo;按钮来更改您的密码</p>
                )}
              </div>

              {/* 注销账户 */}
              <div className="bg-white rounded-xl shadow-sm border border-red-200 p-6">
                <h3 className="text-lg font-semibold text-red-600 mb-4">危险操作</h3>
                
                {!showDeleteConfirm ? (
                  <div>
                    <p className="text-gray-600 mb-4">
                      注销账户将永久删除您的所有数据，包括学习进度、经验值等信息。
                      <span className="text-red-600 font-medium">注销后30天内不能重新注册。</span>
                    </p>
                    <button
                      onClick={() => setShowDeleteConfirm(true)}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm font-medium"
                    >
                      注销账户
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <p className="text-red-600 font-medium">
                      ⚠️ 此操作不可撤销！注销后您的所有数据将被永久删除，且30天内不能重新注册。
                    </p>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        请输入&ldquo;确认注销&rdquo;来确认操作
                      </label>
                      <input
                        type="text"
                        value={deleteConfirmText}
                        onChange={(e) => setDeleteConfirmText(e.target.value)}
                        className="w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                        placeholder="确认注销"
                      />
                    </div>

                    {/* 注销错误提示 */}
                    {deleteError && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-red-700 text-sm">{deleteError}</p>
                      </div>
                    )}

                    {/* 注销成功提示 */}
                    {deleteMessage && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-green-700 text-sm">{deleteMessage}</p>
                      </div>
                    )}

                    <div className="flex space-x-3">
                      <button
                        onClick={handleDeleteAccount}
                        disabled={loading || deleteConfirmText !== '确认注销'}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 text-sm font-medium"
                      >
                        {loading ? '注销中...' : '确认注销账户'}
                      </button>
                      <button
                        onClick={() => {
                          setShowDeleteConfirm(false)
                          setDeleteConfirmText('')
                          setDeleteError('')
                          setDeleteMessage('')
                        }}
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 text-sm font-medium"
                      >
                        取消
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 注销成功模态框 */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
          <div className="bg-white rounded-2xl p-8 max-w-md mx-4 shadow-2xl transform animate-scaleIn">
            <div className="text-center">
              {/* 成功图标 */}
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>

              {/* 标题 */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                账户注销成功！
              </h3>

              {/* 消息内容 */}
              <p className="text-gray-600 mb-6 leading-relaxed">
                您的账户已成功注销。
                <br />
                <span className="text-orange-600 font-medium">30天内不能重复注册</span>
              </p>

              {/* 倒计时提示 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <p className="text-blue-800 text-sm">
                  <span className="inline-flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    3秒后将自动返回主页...
                  </span>
                </p>
              </div>

              {/* 确认按钮 */}
              <button
                onClick={() => window.location.replace('/')}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-6 rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                立即返回主页
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
